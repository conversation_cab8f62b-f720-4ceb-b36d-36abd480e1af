/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'dva';
import { getLStorage } from '@/utils/utils';
import { getScanRecord } from '@/services/inventoryManagement';

export const useScanRecord = props => {
  const { form } = props;
  const { area_id: areaIdProps } = useSelector(({ area }) => area);
  const userInfo = useSelector(({ user }) => user.currentUser.user_info);
  const { branchId } = userInfo;
  // 开启开启驿站片区管理
  const { inn_area_ids, phone, area_ids } = useSelector(({ user }) => user.currentUser.user_info);
  const cacheAreaId = getLStorage(`KB_INN_AREA_${phone}`);
  const isCompany = area_ids == '*';
  const initAreaId = inn_area_ids.includes(cacheAreaId)
    ? cacheAreaId
    : isCompany
      ? '0'
      : inn_area_ids.join(',');
  const [reqYzPayload, setReqYzPayload] = useState({ branchId, area_id: initAreaId });
  const actionRef = useRef();

  const columns = [
    {
      title: '快递单号',
      dataIndex: 'waybill',
      search: false,
      width: 250,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      search: false,
      width: 200,
    },
    {
      title: '取件码',
      dataIndex: 'pickupCode',
      search: false,
      width: 150,
    },
    {
      title: '扫描时间',
      dataIndex: 'scan_time',
      search: false,
      width: 200,
    },
    {
      title: '扫描设备',
      dataIndex: 'devInfo',
      search: false,
      width: 250,
      ellipsis: true,
    },
    {
      title: '删除原因',
      dataIndex: 'type_cn',
      search: false,
      width: 250,
    },
  ];

  const getList = async params => {
    const values = form.getFieldsValue();
    const { current = 1, pageSize = 20, ...rest } = params || {};
    const res = await getScanRecord({
      ...rest,
      ...values,
      page: current,
      page_size: pageSize,
    });
    return res;
  };

  const handleSearch = () => {
    form.validateFields(async err => {
      if (!err) {
        actionRef.current.reload({ current: 1 });
      }
    });
  };

  useEffect(
    () => {
      actionRef.current.reset();
      form.resetFields();
      setReqYzPayload(prevData => ({
        ...(prevData || {}),
        area_id: areaIdProps,
      }));
      handleSearch();
    },
    [areaIdProps],
  );

  return {
    form,
    actionRef,
    columns,
    reqYzPayload,
    getList,
    handleSearch,
  };
};
