/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React from 'react';
import { Button, Form, Input } from 'antd';
import { useScanRecord } from './utils';
import { Yzs } from '@/components/Select';
import { connect } from 'dva';
import ProTable from '@/components/AdaptTable';
import { AdaptFormWrapperFn } from '@/components/AdaptForm';

const ScanRecord = props => {
  const { form, columns, actionRef, reqYzPayload, getList, handleSearch } = useScanRecord(props);
  const { getFieldDecorator } = form;

  return (
    <div>
      <Form form={form} layout="inline">
        <Form.Item label="驿站站点">
          {getFieldDecorator('cm_id', {
            rules: [
              {
                required: true,
                message: '请选择驿站站点',
              },
            ],
          })(
            <Yzs
              style={{ width: '200px' }}
              showAll={false}
              reqPayload={reqYzPayload}
              repeatRequest={false}
              requestInit
              placeholder="请选择驿站"
            />,
          )}
        </Form.Item>
        <Form.Item label="" name="search">
          {getFieldDecorator('search', {
            initialValue: '',
          })(<Input style={{ width: '250px' }} placeholder="请输入取件码/单号/手机号/手机尾号" />)}
        </Form.Item>
        <Form.Item>
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <ProTable
        rowKey="id"
        actionRef={actionRef}
        request={getList}
        columns={columns}
        search={false}
        manual
        scroll={{ x: 1200 }}
      />
    </div>
  );
};
export default AdaptFormWrapperFn(Form.create()(connect()(ScanRecord)));
