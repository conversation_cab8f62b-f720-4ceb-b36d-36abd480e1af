/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
*/
/* eslint-disable prefer-promise-reject-errors */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Modal, Form, Input, Row, Col, Button, message, Typography } from 'antd';
import { useSelector } from 'dva';
import { getGraphicCode, getSmsCode } from '@/services/setup';
import CaptchaNoRobot from '@/components/Captcha/no-robot';
import { check } from '@/utils/patterns';
import './verifyModal.less';

const { Title } = Typography;

const FormItem = Form.Item;

const noop = () => {};

/**
 * 代入库，短信验证弹窗
 *  */
const SmsModal = props => {
  const { key: zyKey } = useSelector(({ setting }) => setting.options);
  const isZyAccount = zyKey === 'post';
  const {
    form,
    onOk = noop,
    onCancel,
    cm_id,
    platform,
    phone,
    password,
    submitting,
    parentPhone,
  } = props;

  const [isGraphic, setIsGraphic] = useState(false);
  const [points, setPoints] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [data, setData] = useState({
    width: 200,
    height: 200,
    img: '',
  });
  const captchaRef = useRef(null);

  const { getFieldDecorator, validateFields, setFieldsValue } = form;

  const handleOk = () => {
    validateFields((err, values) => {
      if (err) return;
      const { code } = values || {};
      onOk({ smsCode: code });
    });
  };

  const getSmsCodeRequest = () =>
    new Promise((resolve, reject) => {
      const { code, message: _msg } = check('phone', phone);
      if (code > 0) {
        reject(new Error(_msg));
        return;
      }
      if (isGraphic && (!points.length && !inputValue)) {
        reject(new Error('请输入图形验证码'));
        return;
      }
      const params = {
        username: phone,
        complate: platform,
        password,
        isZyAccount,
        cm_id,
        phone: parentPhone,
      };
      if (isGraphic) {
        params.captcha_id = data.captchaId;
        params.captcha_code = data.imgType == 1 ? inputValue : points;
      }
      getSmsCode(params).then(res => {
        const { code: resCode } = res;
        if (resCode == 29999) {
          getGraphic();
          reject({ hideToast: true });
          return;
        }
        if (resCode == 29998) {
          message.error(res.msg);
          getGraphic();
          reject({ hideToast: true });
          return;
        }
        resolve(res);
      }, reject);
    });

  const getGraphic = () => {
    getGraphicCode({
      username: phone,
      complate: platform,
      password,
      cm_id,
      phone: parentPhone,
    }).then(res => {
      const { code, data: { img } = {}, msg } = res || {};
      if (code === 0 && img) {
        captchaRef.current.resetCount();
        setIsGraphic(true);
        setData(res.data);
        setPoints([]);
        setInputValue('');
      } else {
        message.error(msg);
      }
    });
  };

  const handleSetPoint = e => {
    if (data.imgType == 1) return;
    if (points.length === 3) return;
    const imgWrap = e.currentTarget;
    const rect = imgWrap.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    setPoints(prevPoints => [...prevPoints, { x, y }]);
  };

  const reset = () => {
    setPoints([]);
    setInputValue('');
    setFieldsValue({
      code: '',
    });
  };

  useEffect(
    () => {
      setFieldsValue({
        phone,
        code: '',
      });
    },
    [phone],
  );

  const modalWidth = useMemo(() => (data?.width ? +data.width + 100 : 300), [data.width]);

  return (
    <Modal
      className="verify-modal"
      confirmLoading={submitting}
      title={`请登录${platform}驿站`}
      width={modalWidth}
      visible={!!phone}
      onOk={handleOk}
      onCancel={onCancel}
      footer={[
        isGraphic && (
          <Button icon="reload" onClick={reset}>
            重置
          </Button>
        ),
        <Button type="primary" onClick={handleOk}>
          登录
        </Button>,
        <Button onClick={onCancel}>取消</Button>,
      ]}
    >
      <Form>
        {isGraphic && (
          <FormItem>
            {data.tip && <Title level={4}>{data.tip}</Title>}
            <div className="imgWrap" onClick={handleSetPoint}>
              {data.imgType == 1 && (
                <Input
                  value={inputValue}
                  onChange={e => setInputValue(e.target.value)}
                  placeholder="请输入结果"
                />
              )}
              <img src={data.img} width={data.width} height={data.height} alt="" />
              {points.map((point, index) => (
                <div
                  className="point"
                  // eslint-disable-next-line react/no-array-index-key
                  key={index}
                  style={{
                    top: point.y - 12,
                    left: point.x - 12,
                  }}
                >
                  {index + 1}
                </div>
              ))}
            </div>
          </FormItem>
        )}
        <FormItem>
          <Row type="flex">
            <Col style={{ flex: 1 }}>
              {getFieldDecorator('phone', {
                initialValue: phone,
                rules: [
                  {
                    required: true,
                    message: '请输入手机号',
                  },
                ],
              })(<Input disabled allowClear placeholder="请输入手机号" />)}
            </Col>
            <Col>
              <CaptchaNoRobot request={getSmsCodeRequest} ref={captchaRef} />
            </Col>
          </Row>
        </FormItem>
        <FormItem>
          {getFieldDecorator('code', {
            rules: [
              {
                required: true,
                message: '请输入短信验证码',
              },
            ],
          })(<Input allowClear placeholder="请输入短信验证码" style={{ width: 250 }} />)}
        </FormItem>
      </Form>
    </Modal>
  );
};

export default Form.create()(SmsModal);
