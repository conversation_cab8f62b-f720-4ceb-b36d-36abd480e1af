/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { editDaopaiSwitch } from '@/services/allocation_setting';
import { Radio } from 'antd';
import React from 'react';

const AllowSecond = props => {
  const handleSet = () => {
    editDaopaiSwitch({
      type: 8,
      switch: props.app_modify_repeat_pi == '1' ? '0' : '1',
    }).then(res => {
      res && props.getSwitchReload && props.getSwitchReload();
    });
  };

  return (
    (props.sto_deny_repeat_pi == '1' || props.zt_deny_repeat_pi == '1') && (
      <div>
        <Radio checked={Boolean(props.app_modify_repeat_pi * 1)} onClick={handleSet}>
          允许业务员在快递APP上手动取消禁止
        </Radio>
      </div>
    )
  );
};

export default AllowSecond;
