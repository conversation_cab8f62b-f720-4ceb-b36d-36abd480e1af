/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import {
  getAllAppIcon,
  getBranchInfo,
  getConInfo,
  getEmsOrgNoList,
} from '@/services/configuration';
import { Popover } from 'antd';
import { getDefaultOrderUser } from '@/services/api';
import { getGunType } from '@/services/allocation';

const brand = {
  label: '网点',
  placeholder: '请选择网点',
  name: 'branch_code',
  rules: [
    {
      required: true,
      message: '请选择网点',
    },
  ],
  type: 'select',
};
const account = {
  label: '工号',
  placeholder: '请输入工号',
  name: 'gun_account',
  rules: [
    {
      required: true,
      message: '请输入工号',
    },
    {
      pattern: /^[a-zA-Z0-9.]{3,15}$/,
      message: '请输入3到15位数字或字母可以包含字符（.）',
    },
  ],
};
const pwd = {
  label: '密码',
  placeholder: '请输入密码',
  name: 'gun_pwd',
  rules: [
    {
      // eslint-disable-next-line no-useless-escape
      pattern: /^[0-9a-zA-Z`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]{1,20}$/,
      message: '请输入1到20位数字或字母或者特殊符号',
    },
  ],
};
const code = {
  label: '三段码',
  placeholder: '请输入三段码',
  name: 'third_code',
  rules: [
    {
      // eslint-disable-next-line no-useless-escape
      pattern: /^[0-9a-zA-Z`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]{1,15}$/,
      message: '请输入1到15位数字或字母或者特殊符号',
    },
  ],
};
const phone = {
  label: '手机号',
  name: 'gun_account_v2',
  rules: [
    {
      pattern: /^1[2-9]{1}[0-9]{1}\d{8}$/,
      message: '请输正确的手机号',
    },
  ],
};

export const brandsMap = {
  sto: {
    brand: 'sto',
    label: '申通',
    formList: [
      brand,
      {
        ...account,
        placeholder: '请输入申通工号',
        help: '示例：**********',
      },
      {
        label: '小件员账号',
        placeholder: '请输入小件员账号',
        name: 'modalSelect',
        ModalConfig: {
          title: '申通小件员账号设置',
          field: [
            {
              placeholder: '请输入申行者小件员版账号',
              name: 'gun_account_v2',
              rules: [
                {
                  required: true,
                  message: '请输入申行者小件员版账号',
                },
              ],
            },
            {
              placeholder: '请输入验证码',
              withCaptcha: true,
              name: 'sms_code',
              rules: [
                {
                  required: true,
                  message: '请输入验证码',
                },
              ],
            },
          ],
        },
      },
      code,
    ],
  },
  zt: {
    brand: 'zt',
    label: '中通',
    formList: [
      brand,
      {
        ...account,
        placeholder: '请输入中通工号',
        help: '示例：37123.123',
      },
      { ...pwd, label: 'PDA密码', placeholder: '请输入PDA密码' },
      {
        ...phone,
        placeholder: '请输入掌中通手机号',
      },
      {
        ...pwd,
        name: 'gun_pwd_v2',
        placeholder: '请输入掌中通密码',
      },
      code,
      {
        label: 'PDA设备编号',
        placeholder: '请输入PDA设备编号',
        name: 'modalSelect',
        ModalConfig: {
          title: 'PDA设备编号设置',
          field: [
            {
              placeholder: '请输入PDA设备编号（SN）',
              name: 'pda_dev_imei',
              rules: [
                {
                  required: true,
                  message: '请输入PDA设备编号',
                },
              ],
            },
            {
              placeholder: '请输入中通PDA对应设备型号（如：NLS-NFT10）',
              name: 'pda_dev_id',
              rules: [
                {
                  required: true,
                  message: '请输入中通PDA对应设备型号',
                },
              ],
            },
          ],
        },
      },
    ],
  },
  yd: {
    brand: 'yd',
    label: '韵达',
    formList: [
      brand,
      {
        ...account,
        label: '编码',
        placeholder: '请输入业务员编码',
        help: '示例：1221，大掌柜系统内查看',
      },
      {
        ...pwd,
        label: '口令号',
        placeholder: '请输入口令号',
        rules: [...pwd.rules, { required: true, message: '请输入口令号' }],
      },
      {
        ...phone,
        placeholder: '请输入快递员揽派手机号',
      },
      {
        ...pwd,
        name: 'gun_pwd_v2',
        placeholder: '请输入快递员揽派密码',
      },
      code,
    ],
  },
  yt: {
    brand: 'yt',
    label: '圆通',
    formList: [
      brand,
      {
        ...account,
        help: '示例：********',
      },
      {
        ...pwd,
        label: '行者密码',
        placeholder: '请输入行者密码',
        rules: [...pwd.rules, { required: true, message: '请输入行者密码' }],
      },
      {
        ...phone,
        placeholder: '请输入行者手机号',
      },
      { ...pwd, name: 'gun_pwd_v2', label: '尊者密码', placeholder: '请输入尊者密码' },
      code,
      {
        label: '设备型号',
        placeholder: '请输入巴枪设备型号',
        name: 'device_model',
        tooltip: '请在巴枪手机设置->关于手机->型号 中查看',
      },
      {
        label: '设备编号',
        placeholder: '请输入巴枪设备编号',
        name: 'device_imei',
        tooltip:
          '工业手机->设置->关于手机->状态->IMEI信息(如果有2个，选择插槽1)或者工业手机背面标签IMEI编号',
      },
    ],
  },
  jt: {
    brand: 'jt',
    label: '极兔',
    formList: [
      brand,
      {
        ...account,
        placeholder: '请输入极兔工号',
        help: '示例：********',
      },
      {
        ...pwd,
        placeholder: '请输入内场pro密码',
        rules: [...pwd.rules, { required: true, message: '请输入内场pro密码' }],
      },
      code,
      {
        label: '设备编号',
        name: 'imei',
        placeholder: '请输入上述工号对应的极兔场外pro设备编号',
        extra: (
          <div>
            极兔场外pro安卓版登录页获取,{' '}
            <Popover
              content={
                <img
                  src="https://cdn-img.kuaidihelp.com/kb_city_web/jt_extra.png"
                  alt=""
                  style={{ width: 595 / 2, height: 1284 / 2 }}
                />
              }
            >
              <a>示例</a>
            </Popover>
          </div>
        ),
      },
    ],
  },
  ems: {
    brand: 'ems',
    label: '邮政',
    formList: [
      brand,
      { ...account, placeholder: '请输入邮政工号', help: '示例：********' },
      {
        ...pwd,
        placeholder: '请输入中邮揽投密码',
        rules: [...pwd.rules, { required: true, message: '请输入中邮揽投密码' }],
      },
      {
        name: 'gun_account_v2',
        label: '设备编码',
        placeholder: '请输入中邮揽投设备编码',
      },
      {
        ...pwd,
        name: 'trace_code',
        label: '道段编号',
        placeholder: '请输入道段编号',
      },
      code,
      {
        label: '机构号',
        name: 'org_no',
        placeholder: '请输入机构号',
        type: 'select',
      },
    ],
  },
};

const transformList = async list => {
  if (Array.isArray(list)) {
    // 是否绑定自动自动发单，影响到申通三段码展示
    const { user_type } = await getDefaultOrderUser();
    const isBind = !!user_type;

    return Object.values(brandsMap).map(item => {
      const current = list.find(v => v.brand === item.brand);

      // 已绑定自动发单，申通增加三段码显示
      if (item.brand == 'sto') {
        item.formList = item.formList.filter(i => {
          if (i.name == 'third_code') {
            return isBind;
          }
          return true;
        });
      }

      if (current) {
        if (current.devs?.imei) {
          current.imei = current.devs.imei;
        }
        return {
          ...item,
          ...current,
        };
      }
      return {
        ...item,
        is_open: 0,
      };
    });
  }
  return [];
};
export const getConList = courier_no =>
  getConInfo({ kc_code: courier_no }).then(res => transformList(res.data));

export const getBrandOptions = params =>
  getBranchInfo(params).then(res => {
    if (res.code === 0 && res.data && Array.isArray(res.data[params.brand])) {
      return res.data[params.brand].map(item => ({
        label: item,
        value: item.split('-')[0],
      }));
    }
    return [];
  });

export const getEmsOrgNoOptions = params =>
  getEmsOrgNoList(params).then(res => {
    if (res.code === 0 && res.data && Array.isArray(res.data)) {
      return res.data.map(item => ({
        label: item.org_no,
        value: item.org_no,
      }));
    }
    return [];
  });

export const getGuns = params => {
  return getGunType(params).then(res => {
    if (res.code === 0 && res.data && Array.isArray(res.data)) {
      return res.data.map(item => ({
        label: item.type || item.name,
        value: item.type || item.name,
      }));
    }
    return [];
  });
};

export const checkBrands = refList => {
  let flag = true;
  refList.forEach(item => {
    if (item?.props?.data?.is_open != 1) return;
    item.validateFields(err => {
      if (err) {
        flag = false;
      }
    });
  });
  return flag;
};

const appIcons = [
  { type: 'top', label: '顶部功能' },
  { type: 'middle', label: '中间功能' },
  { type: 'bottom', label: '底部功能' },
];
export const getAllIcon = () =>
  getAllAppIcon().then(res => {
    const arr = [];
    if (res.code == 0 && Array.isArray(res.data)) {
      res.data.forEach(item => {
        appIcons.forEach(icon => {
          if (item.type === icon.type) {
            const index = arr.findIndex(v => v.type === item.type);
            if (index === -1) {
              arr.push({ ...icon, child: [item] });
            } else {
              arr[index].child.push(item);
            }
          }
        });
      });
      return arr;
    }
    return [];
  });

export const transformParams = (list, obj = {}, courier_no) =>
  list.filter(item => item.is_open == 1).map(item => {
    const v = JSON.parse(JSON.stringify(item));
    const values = obj?.[item.brand] || {};
    delete v.formList;
    delete v.label;
    return {
      brand: item.brand || '',
      gun_account: values.gun_account || '',
      gun_pwd: values.gun_pwd || '',
      third_code: values.third_code || '',
      branch_code: values.branch_code || '',
      kc_code: courier_no,
      gun_account_v2: values.gun_account_v2,
      gun_pwd_v2: values.gun_pwd_v2 || '',
      trace_code: values.trace_code || '',
      imei: values.imei || '',
      pda_dev_imei: item.pda_dev_imei || '',
      pda_dev_id: item.pda_dev_id || '',
      org_no: values.org_no || '',
      device_model: values.device_model || '',
      device_imei: values.device_imei || '',
      ...(values.modalSelect || {}),
    };
  });
