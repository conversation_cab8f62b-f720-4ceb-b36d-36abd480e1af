/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import logo from '@/assets/logo.svg'; // 快宝logo
// 中邮图片
import logoPost from '@/assets/logo-post.png';
import loginBgPost from '@/assets/login-bg-post.png';
import logoYjy from '@/assets/logo-yjy.png';
import loginBgYjy from '@/assets/login-bg-yjy.png';
import logoCctjs from '@/assets/logo-cctjs.png';
import logoQhd from '@/assets/logo-qhd.png';
import ykfLogo from '@/assets/ykf-screen.png';
import logoSY from '@/assets/logo-sy2.png';
import logokhy from '@/assets/logo-khy.png';
import khyLogo from '@/assets/khy-screen.jpg';
import yzLogo from '@/assets/bigScreen/yzScreen/yzLogo.png';
import logoDxf from '@/assets/logo-dxf.jpg';
import { defaultBgUrl, publicPath } from './defaultSettings';
import { getOptByHost } from '@/utils/getLocationParams';
import {
  deleteStationForPartner,
  getOthersStationList,
  getZyStationList,
  saveStationForPartner,
} from './services/api';
import { setIcon } from './utils/utils';

// 默认配置
const defaultConfig = {
  key: 'yz',
  name: '快宝智慧快递管理平台',
  copyright: '快宝（上海）网络技术有限公司',
  icpNo: '',
  recordNo: '沪公网安备 **************号',
  recordNumber: **************,
  logo,
  loginBg: {
    url: defaultBgUrl,
    height: 388,
  },
  postName: '快宝',
  recruitUrl: 'https://www.kuaidihelp.com/index/joinStation',
  appUrl: '//www.kuaidihelp.com/index/station',
  favicon: '/dist/favicon.png',
  primaryColor: '#1890FF',
  topAccountTitle: '快宝公司',
  enableRegion: process.env.NODE_ENV === 'development',
  waterMarkPath: ['post/setup#MixInStorage', 'post/list#MixInStatistics'],
};

const dxf = {
  key: 'dxf',
  name: '递先锋智慧管理平台',
  copyright: '客货邮融合平台',
  icpNo: '',
  recordNo: '',
  recordNumber: '',
  logo: logoDxf,
  postName: '递先锋',
  allocationName: '递先锋快递员',
  favicon: `${publicPath}favicon-dxf.jpg`,
  enableArea: true,
  screenConfig: {
    title: '递先锋',
  },
  recaptcha: {
    v3key: '6LeCASkhAAAAALfyCATci9v0ThP6neJ3zDGmKdeq',
    v2key: '6LeBASkhAAAAAFjmCkNjD4dMrz_ni1yt2oRgsAoi',
  },
  hidePostAreaTab: ['apply'],
  newPostAreaNotice: true,
  enableRegion: false,
  postSetupPanes: ['MixInStorage'],
  enablePostLogin: true,
  enableGpDiscount: true,
  hideHelpAndService: true,
  hideServiceCustomize: true,
}

// 配置映射表（包含域名和shop_id配置）
const configMap = {
  'city.kuaidihelp.com': {
    title: '快宝智慧快递管理平台-快递末端企业专用管理系统',
    showAdv: true, // 广告位 Advertisement，login左侧
    enableRegion: true, // 片区功能
    enableArea: true, // 站点管理 - 区域选择
    appName: '快递员', // app标识名称
    enablePostLogin: true, // 登录驿站网页版
    recaptcha: {
      v3key: '6LeCASkhAAAAALfyCATci9v0ThP6neJ3zDGmKdeq',
      v2key: '6LeBASkhAAAAAFjmCkNjD4dMrz_ni1yt2oRgsAoi',
    },
    enableGpDiscount: true, // 共配折扣设置
    enableOperatorKey: true, // 基础数据-业务员 快宝工号
    screenConfig: {
      logo: {
        url: yzLogo,
      },
    },
  },
  'gp.chinapostcnps.com': {
    key: 'post',
    name: '中邮共配管理系统',
    copyright: '中邮驿站',
    icpNo: '浙ICP备********号-2',
    recordNo: '',
    recordNumber: '',
    logo: logoPost,
    loginBg: {
      url: loginBgPost,
      height: 388,
    },
    postName: '中邮',
    allocationName: '中邮驿路',
    recruitUrl: '',
    appUrl: 'http://renew.kuaidihelp.com/rn/postman.apk',
    favicon: '/dist/favicon-post.png',
    primaryColor: '#b8d637',
    topAccountTitle: '中邮总公司',
    enableRegion: false, // 片区功能
    api: {
      getStationList: getZyStationList,
    },
    recaptcha: {
      v3key: '6Lc-YiohAAAAACCJjG0AxCXmSfla79fF93akXEpL',
      v2key: '6LfOYSohAAAAAIUYkfNYXKppbEqYvNctotnss8zP',
    },
    postSetupPanes: ['MixInStorage'], // 设置和管理tabs
    appName: '中邮驿路',
    enablePostLogin: true,
    enableGpDiscount: true,
  },
  'yikuaisaoplus-test.hehengjia.com': {
    key: 'yjy',
    name: '驿加易共配管理系统',
    copyright: '驿加易',
    icpNo: '浙ICP备********号-2',
    recordNo: '',
    recordNumber: '',
    logo: logoYjy,
    loginBg: {
      url: loginBgYjy,
      height: 388,
    },
    postName: '驿加易',
    recruitUrl: '',
    appUrl: '',
    favicon: '/dist/favicon-yjy.png',
    primaryColor: '#b8d637',
    topAccountTitle: '驿加易总公司',
    enableRegion: false, // 片区功能
    api: {
      getStationList: getOthersStationList,
      deleteStation: deleteStationForPartner,
      saveStation: saveStationForPartner,
    },
    recaptcha: {
      v3key: '6Lda5SohAAAAAA1dGMTfd_QWwS2538AA7VkYRMbM',
      v2key: '6LdS5SohAAAAAHiw729-SFgcWF6ApR9OBB8R6FN9',
    },
    enablePostLogin: true,
    enableGpDiscount: true,
  },
  'city.tongdiyiyou.com': {
    key: 'yz',
    subKey: 'td',
    name: '智慧快递管理平台',
    copyright: '上海通递驿优网络技术有限公司',
    icpNo: '沪ICP备********号-1',
    recordNo: '',
    recordNumber: '',
    logo,
    loginBg: {
      url: defaultBgUrl,
      height: 388,
    },
    postName: '驿站管理',
    recruitUrl: '',
    appUrl: '',
    favicon: '/dist/favicon.png',
    primaryColor: '#1890FF',
    showAdv: true,
    recaptcha: {
      v3key: '6LeEP4IhAAAAADKfwWJ6-resyTPvrVsSE8wzTxD0',
      v2key: '6LceQYIhAAAAAPXGIOah0_tqb6XQAhMcitP5DHq0',
    },
    enableRegion: true,
  },
  // 'gl.cctjs.cn': {
  'gl.cctjs.cn': {
    key: 'cctjs',
    name: '村村通驿站管理后台',
    copyright: '村村通驿站',
    icpNo: '浙ICP备2023053374号-2',
    recordNo: '',
    recordNumber: '',
    logo: logoCctjs,
    loginBg: {
      url: logoCctjs,
      height: 388,
    },
    postName: '村村通',
    allocationName: '村村通快递员',
    favicon: `${publicPath}favicon-cct.png`,
    enableArea: true,
    screenConfig: {
      title: '江山市快递进村',
    },
    recaptcha: {
      v3key: '6LeCqC8pAAAAAPuEPixq7FTlxmbz6DMe3KPFJYB7',
      v2key: '6LfsqC8pAAAAAPMEp1nhR_ViFvjLNr-EBPwPB8sb',
    },
    hideDispatTab: ['serviceSettings', 'serviceRecord'],
    hidePostAreaTab: ['apply'],
    newPostAreaNotice: true,
    enableRegion: true,
    postSetupPanes: ['MixInStorage'],
  },
  'www.yikuaifa.cn': {
    // 秦皇岛 - 驿快发 - 驿宝
    key: 'qhd',
    name: '驿宝驿站',
    title: '驿宝管理平台',
    copyright: '驿宝驿站',
    icpNo: '',
    recordNo: '',
    recordNumber: '',
    logo: logoQhd,
    loginBg: {
      url: logoQhd,
      height: 512,
    },
    postName: '驿宝',
    favicon: `${publicPath}logo-qhd.png?timestamp=01`,
    enableArea: true,
    screenConfig: {
      logo: {
        url: ykfLogo,
        width: 'auto',
        height: 42,
      },
      hideSubtitle: true,
      title: '驿宝驿站',
      chart_left_3: 'rank_3',
      chart_mid: {
        in_num: {
          label: '入库',
          color: '#d8af30',
        },
        out_num: {
          label: '出库',
          color: '#3591e6',
        },
      },
      chart_right_1: 'station',
    },
    postSetupPanes: ['pushSet', 'NotificationManagement', 'ScanCode'],
    recaptcha: {
      v3key: '6LeCASkhAAAAALfyCATci9v0ThP6neJ3zDGmKdeq',
      v2key: '6LeBASkhAAAAAFjmCkNjD4dMrz_ni1yt2oRgsAoi',
    },
    hideDispatTab: ['storageFeeSetting', 'storageFeeStatistics', 'storageFeeDistribution'],
    hidePostAreaTab: ['apply', 'regional'],
    newPostAreaNotice: true,
    enableRegion: true,
  },
  'sygp.zckuaidi.com': {
    key: 'sy',
    name: '送悦智慧快递管理平台',
    title: '送悦智慧快递管理平台—快递末端企业专用管理系统',
    copyright: '湖南送悦科技有限公司',
    icpNo: '京ICP备17073467号',
    recordNo: '',
    recordNumber: '',
    postName: '送悦',
    logo: logoSY,
    loginBg: {
      url: logoSY,
      height: 512,
    },
    favicon: logoSY,
    recaptcha: {
      v3key: '6LcZIeIpAAAAAIsZg_EJjFZRAL-0aEOmEZcdtkg5',
      v2key: '6LcjIeIpAAAAANyskHEOfu7D7-VZ18EJlyW3sxxe',
    },
    hideDispatTab: ['storageFeeSetting', 'storageFeeStatistics', 'storageFeeDistribution'],
    hidePostAreaTab: ['apply', 'regional'],
    newPostAreaNotice: true,
    enableArea: true,
    appName: '送悦快递员',
    enableGpDiscount: true,
    enableRegion: true,
    postSetupPanes: ['regionalAgent', 'NotificationManagement', 'MixInStorage', 'ScanCode'],
  },
  'khy.kuaidihelp.com': {
    key: 'khy',
    name: '客货邮融合平台',
    copyright: '客货邮融合平台',
    icpNo: '',
    recordNo: '',
    recordNumber: '',
    logo: khyLogo,
    loginBg: {
      url: logokhy,
      height: 388,
    },
    postName: '客货邮',
    allocationName: '客货邮快递员',
    favicon: khyLogo,
    enableArea: true,
    screenConfig: {
      title: '客货邮融合平台',
    },
    recaptcha: {
      v3key: '6LdmPD4qAAAAAJl2Qo1OhfICksZTG061lx0dO7Re',
      v2key: '6LemPT4qAAAAAKwtuNu2wHoxHb-vpLDiDM3_Nbjo',
    },
    hideDispatTab: ['serviceSettings', 'serviceRecord'],
    hidePostAreaTab: ['apply'],
    newPostAreaNotice: true,
    enableRegion: true,
  },
  4601: dxf, // 测试环境
  3816: dxf, // 生产环境
};

// 生成最终的options配置
const options = {
  ...defaultConfig,
  ...getOptByHost(configMap),
  // 保存配置映射表和默认配置，供动态更新使用
  __configMap__: configMap,
  __defaultConfig__: defaultConfig,
};

setIcon(options.favicon);

module.exports = {
  options,
};
