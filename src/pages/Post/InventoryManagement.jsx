/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { PureComponent } from 'react';
import StandardTab from '@/components/StandardTab';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
import { Col, Row } from 'antd';
import { connect } from 'dva';
import DispatchPianqu from '@/components/_pages/Post/Area/DispatchPianqu';
import styles from './style.less';
import Inventory from '@/components/_pages/Post/inventoryManagement/inventory';
import ScanRecord from '@/components/_pages/Post/inventoryManagement/scan';

@connect(() => ({}))
export default class InventoryManagement extends PureComponent {
  state = {};

  handleTabChange = activeKey => {
    this.setState({
      activeKey,
    });
  };

  onChange = area_id => {
    this.props.dispatch({
      type: 'area/changeArea',
      payload: { area_id },
    });
  };

  render() {
    const { activeKey } = this.state;
    return (
      <PageHeaderLayout
        title={
          <Row type="flex" justify="space-between">
            <Col>库存记录</Col>
            <Col>
              <DispatchPianqu type="inn" onChange={this.onChange} />
            </Col>
          </Row>
        }
      >
        <div className={`${styles.main}`}>
          <StandardTab
            name="inventoryManagement"
            activeKey={activeKey}
            onChange={this.handleTabChange}
            destroyInactiveTabPane
            panes={[
              {
                tab: '库存记录',
                component: <Inventory />,
              },
              {
                tab: '扫描未入库记录',
                component: <ScanRecord />,
              },
            ]}
          />
        </div>
      </PageHeaderLayout>
    );
  }
}
