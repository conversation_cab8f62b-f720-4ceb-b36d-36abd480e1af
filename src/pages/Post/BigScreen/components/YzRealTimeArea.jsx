/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 新零售，顶部实时数据
import React, { useCallback, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'dva';
import { useInterval } from 'ahooks';
import { Row, Col } from 'antd';
import CountUp from '@/components/CountUp';
import NumberDelay from '@/components/NumberDelay';
import CommonBorder from './Wrappers/CommonBorder';
import UpOrDown from './Wrappers/UpOrDown';
import styles from './YzRealTimeArea.less';

const Detail = React.memo(
  ({ type, title, loading, top = 0, sub = 0, total = 0, rate, cacheTag, lastTen }) => {
    const yb = type == 'stage_num' || type == 'fans_num';
    useEffect(
      () => {
        // 字体自适应
        function changeScale(numDom, boxDom) {
          const boxWidth = boxDom && boxDom.offsetWidth;
          const numWidth = numDom && numDom.offsetWidth;
          if (numWidth > boxWidth) {
            const scaleRate = boxWidth / numWidth;
            numDom && (numDom.style.transform = `scale(${scaleRate})`);
          } else {
            numDom && (numDom.style.transform = 'scale(1)');
          }
        }
        if (loading) {
          setTimeout(() => {
            const boxDom1 = document.getElementById(`boxDom_${type}_1`);
            const numDom1 = document.getElementById(`numDom_${type}_1`);
            const boxDom2 = document.getElementById(`boxDom_${type}_2`);
            const numDom2 = document.getElementById(`numDom_${type}_2`);
            const boxDom3 = document.getElementById(`boxDom_${type}_3`);
            const numDom3 = document.getElementById(`numDom_${type}_3`);
            changeScale(numDom1, boxDom1);
            changeScale(numDom2, boxDom2);
            changeScale(numDom3, boxDom3);
          }, 1500);
        }
      },
      [type, loading],
    );

    return (
      <CommonBorder showBg title={title} loading={loading}>
        {yb ? (
          <Row>
            <Col>
              <div className={styles.yesterday}>{sub}</div>
            </Col>
            <Col>
              <div className={`${styles.yesterday + ' ' + styles.small}`}>
                累计：
                {total}
              </div>
            </Col>
          </Row>
        ) : (
          <Row className={styles.content}>
            <Col id={`boxDom_${type}_1`} className="firstNum">
              <CountUp
                id={`numDom_${type}_1`}
                className={styles.firstNum}
                cacheInLocal
                duration={10 * 60}
                ratio={lastTen ? 1 : 0.7}
                start={[lastTen || top, cacheTag]}
                end={top}
                format={type == 'express_fee' ? '0,0.00' : ''}
              />
            </Col>
            <Col>
              <Row type="flex">
                <Col style={{ minWidth: 60 }}>昨日：</Col>
                <Col id={`boxDom_${type}_2`} style={{ width: 'calc(100% - 60px)' }}>
                  <span id={`numDom_${type}_2`}>
                    <NumberDelay format={type == 'express_fee' ? '0,0.00' : ''} number={sub} />
                  </span>
                </Col>
              </Row>
            </Col>
            {rate !== null ? (
              <Col>
                <Row type="flex">
                  <Col style={{ minWidth: 60 }}>环比：</Col>
                  <Col id={`boxDom_${type}_3`} style={{ width: 'calc(100% - 60px)' }}>
                    <UpOrDown id={`numDom_${type}_3`} rate={rate} />
                  </Col>
                </Row>
              </Col>
            ) : null}
          </Row>
        )}
      </CommonBorder>
    );
  },
);

const YzRealTimeArea = () => {
  const dispatch = useDispatch();
  const token = useSelector(({ global } = {}) => global.screenToken);
  const initialValue = useSelector(({ chartData } = {}) => chartData.yzTopRealData);

  const [data, setData] = useState(initialValue.slice(0, 4));
  const [loading, setLoading] = useState(false);

  const fetchData = useCallback(
    () => {
      setLoading(true);
      dispatch({
        type: 'chartData/getTopRealTimeData',
        payload: {
          token,
        },
      })
        .then(res => {
          setData(res);
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    },
    [dispatch, token],
  );

  useEffect(fetchData, [fetchData]);

  useInterval(fetchData, 10 * 60 * 1000);

  return (
    <Row type="flex" gutter={[20, 0]}>
      {data.map(({ key, title, today, yesterday, dod_ratio, today_last, total }) => (
        <Col key={key} span={6}>
          <Detail
            type={key}
            title={title}
            lastTen={today_last} // 前十分钟的数据
            top={today}
            sub={yesterday}
            total={total}
            rate={dod_ratio}
            cacheTag={`${token}_${key}`}
            loading={loading}
          />
        </Col>
      ))}
    </Row>
  );
};

export default YzRealTimeArea;
