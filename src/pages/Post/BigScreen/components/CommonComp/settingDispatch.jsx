/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { setStorageSync } from '@/utils/storage';
import { Button, Checkbox, Dropdown, Menu, Row } from 'antd';
import React, { useEffect, useState } from 'react';

const list = [
  { key: 1, title: '每日入库量' },
  { key: 2, title: '3日出库率' },
  { key: 3, title: '每日寄件量' },
  { key: 4, title: '每日新增站点数' },
  { key: 5, title: '驿站数量' },
  { key: 6, title: '3日出库排名' },
  { key: 7, title: '昨日入库品牌占比' },
  { key: 8, title: '昨日入库量排名' },
];
export const dashboardSettingKey = 'kb-dashboardSetting';
const SettingDispatch = ({ value, onChange }) => {
  const [checkedList, setCheckedList] = useState([]);
  const [visible, setVisible] = useState(false);

  const handleClick = key => {
    if (checkedList.includes(key)) {
      setCheckedList(checkedList.filter(item => item !== key));
    } else {
      setCheckedList([...checkedList, key]);
    }
  };

  const onSubmit = () => {
    if (checkedList.length < 6) return;
    setVisible(false);
    const _checkedList = checkedList.sort((a, b) => a - b);
    setStorageSync(dashboardSettingKey, _checkedList);
    onChange(_checkedList);
  };

  useEffect(
    () => {
      setCheckedList(value);
    },
    [value],
  );

  return (
    <Dropdown
      trigger={['click']}
      placement="bottomRight"
      visible={visible}
      overlay={
        <Menu>
          <div style={{ paddingLeft: 6 }}>选择显示的数据（至少6项）</div>
          {list.map(item => (
            <Menu.Item key={item.key}>
              <Checkbox
                checked={checkedList.includes(item.key)}
                onClick={() => handleClick(item.key)}
              >
                {item.title}
              </Checkbox>
            </Menu.Item>
          ))}
          <Menu.Divider />
          <Row type="flex" justify="end">
            <Button size="small" onClick={() => setVisible(false)}>
              取消
            </Button>
            <Button
              size="small"
              type="primary"
              style={{ margin: '0 8px' }}
              onClick={onSubmit}
              disabled={checkedList.length < 6}
            >
              确定
            </Button>
          </Row>
        </Menu>
      }
    >
      <Button size="small" icon="setting" onClick={() => setVisible(!visible)} />
    </Dropdown>
  );
};

export default SettingDispatch;
