import React, { useEffect, useRef, useState } from 'react';
import { Chart, Geom, Axis, Tooltip, Legend } from 'bizcharts';
import { useSelector } from 'dva';
import { getStationNum } from '@/services/chartData';
import dayjs from 'dayjs';
import { useInterval } from 'ahooks';
import NoDate from './Wrappers/NoDate';
import CommonBorder from './Wrappers/CommonBorder';
import { gradualTextColumn } from './_utils';

const StationNum = props => {
  const { isParentFull } = props;
  const timer = useRef();
  const heightRef = useRef();
  const [height, setHeight] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const { shop_id } = useSelector(({ global }) => global.screenLoginInfo);
  const { screenToken: token } = useSelector(({ global }) => global);

  const cols = {
    date: {
      type: 'cat',
    },
  };

  const label = {
    textStyle: {
      fill: gradualTextColumn, // 文本的颜色
      fontSize: '14', // 文本大小
      fontWeight: 'bold', // 文本粗细
    },
  };

  const fetchData = () => {
    setLoading(true);
    getStationNum({ shop_id, token })
      .then(res => {
        if (res.code == 0 && Array.isArray(res.data)) {
          const _data = [];
          res.data.forEach(item => {
            _data.push(
              ...[
                {
                  date: item.date,
                  type: '入库驿站',
                  value: item.in_station_num,
                },
                {
                  date: item.date,
                  type: '全部驿站',
                  value: item.total_station_num,
                },
              ],
            );
          });
          setData(_data);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(
    () => {
      shop_id && fetchData();
    },
    [shop_id],
  );

  useEffect(
    () => {
      timer.current = setTimeout(() => {
        heightRef.current && setHeight(heightRef.current.clientHeight);
      }, 1000);
      return () => {
        timer.current && clearTimeout(timer.current);
      };
    },
    [isParentFull, heightRef],
  );

  useInterval(fetchData, 60 * 60 * 1000);

  const hasData = data.length > 0;

  return (
    <CommonBorder title="驿站数量" loading={loading}>
      <div style={{ height: '100%' }} ref={heightRef}>
        {hasData ? (
          <Chart
            height={height}
            data={data}
            scale={cols}
            forceFit
            padding={['auto', 'auto', 'auto', 'auto']}
            style={{ backgroundColor: 'rgba(15, 24, 55, 0.7)' }}
          >
            <Legend position="top-right" />
            <Axis name="date" label={{ ...label, formatter: val => dayjs(val).format('DD') }} />
            <Axis
              name="value"
              label={{
                ...label,
                formatter: val => `${val}`,
              }}
            />
            <Tooltip
              useHtml
              crosshairs={{
                type: 'y',
              }}
            />
            <Geom type="line" position="date*value" size={2} shape="smooth" color="type" />
            <Geom
              type="point"
              position="date*value"
              size={4}
              shape="circle"
              color="type"
              style={{
                stroke: '#fff',
                lineWidth: 1,
              }}
            />
          </Chart>
        ) : (
          <NoDate title="暂无数据" style={{ height }} />
        )}
      </div>
    </CommonBorder>
  );
};

export default StationNum;
