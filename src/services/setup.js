/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isLegalData } from '@/utils/utils';
import { message } from 'antd';
import request from '../utils/request';

/** 代入库，列表 */
export async function getMixInList(params) {
  const { isZyAccount = false } = params;
  return request(isZyAccount ? '/Api/ChinaPost/Dak/complate' : '/Api/YZ/League/complate', {
    method: 'POST',
    body: params,
  }).then(res => {
    const { code, data = {}, msg } = res;
    const { list, total, page } = data || {};
    if (code != 0) {
      message.error(msg);
    }
    return {
      list: isLegalData(list),
      total,
      page,
    };
  });
}
/** 代入库，获取弹窗配置 */
export async function getMixInDetail(params) {
  const { isZyAccount = false } = params;
  return request(
    isZyAccount ? '/Api/ChinaPost/Dak/complateDetail' : '/Api/YZ/League/complateDetail',
    {
      method: 'POST',
      body: params,
    },
  ).then(res => {
    const { data = [], msg } = res;
    const { info = [], phone, cm_id, platform_brand = {}, ecommerce } = data || {};
    const arr = [];
    Object.keys(platform_brand).forEach(v => {
      arr.push({
        label: platform_brand[v],
        value: v,
      });
    });
    const resData = {
      phone,
      cm_id,
      info: [],
      platform_brand: arr,
      brands: platform_brand,
      ecommerce,
    };

    if (res.code == 0) {
      isLegalData(info).forEach(item => {
        const { status } = item;
        item.checked = Boolean(status == 1);
      });
      resData.info = info;
      return resData;
    }
    message.error(msg);
    return resData;
  });
}
/** 代入库，新增或修改 */
export async function addMixedIn(params) {
  const { isZyAccount = false } = params;
  return request(isZyAccount ? '/Api/ChinaPost/Dak/complateAdd' : '/Api/YZ/League/complateAdd', {
    method: 'POST',
    body: params,
  });
}

/** 场地扫描管理，快递员列表 */
export async function getSiteScanList(params) {
  return request('/Api/YZ/League/sitescan', {
    method: 'POST',
    body: params,
  }).then(res => {
    const { code, data = {}, msg } = res;
    const { list, total } = data || {};
    if (code != 0) {
      message.error(msg);
    }
    return {
      list: isLegalData(list),
      total,
    };
  });
}

/** 场地扫描管理，添加快递员 */
export async function siteScanAdd(params) {
  return request('/Api/YZ/League/sitescanAdd', {
    method: 'POST',
    body: params,
  });
}
/** 场地扫描管理，快递员启用/禁用 */
export async function siteScanEdit(params) {
  return request('/Api/YZ/League/sitescanEdit', {
    method: 'POST',
    body: params,
  });
}
/** 场地扫描管理，货架设置框数量详情 */
export async function getFrameDetail(params) {
  return request('/Api/YZ/League/frameDetail', {
    method: 'POST',
    body: params,
  });
}
/** 场地扫描管理，货架框数量设置 */
export async function frameEdit(params) {
  return request('/Api/YZ/League/frameEdit', {
    method: 'POST',
    body: params,
  });
}
/** 代入库，发送短信验证码 */
export async function getSmsCode(params) {
  const { isZyAccount } = params;
  return request(isZyAccount ? '/Api/ChinaPost/Dak/sendSmsCode' : '/Api/YZ/League/sendSmsCode', {
    method: 'POST',
    body: params,
  });
}

// 代入库数据统计
export async function getMixinStatistics(params, accountKey) {
  return new Promise(resolve => {
    request(
      accountKey === 'post'
        ? '/Api/ChinaPost/Dak/complateStatistics'
        : '/Api/YZ/League/complateStatistics',
      {
        method: 'POST',
        body: params,
      },
    )
      .then(({ code, data, msg }) => {
        if (code > 0 && msg) {
          message.error(msg);
        }
        if (code == 0 && data) {
          resolve({
            list: Array.isArray(data?.list) ? data.list : [],
            pagination: {
              total: +(data?.total ?? 0),
              pageSize: +(data?.pageSize || data?.size || 20),
              showQuickJumper: false,
              current: +(data?.page ?? 1),
            },
          });
        } else {
          resolve({});
        }
      })
      .catch(() => {
        resolve({});
      });
  });
}
export async function exportMixinStatistics(params) {
  return request('/Api/YZ/ExcelTask/create', {
    method: 'POST',
    body: params,
  });
}
export async function getStatisticsOptions(params) {
  return new Promise(resolve => {
    request('/Api/YZ/League/complateBrandAll', {
      method: 'POST',
      body: params,
    }).then(({ code, data }) => {
      if (code == 0) {
        if (data?.list && Object.prototype.toString.call(data.list) === '[object Object]') {
          const arr = [];
          Object.keys(data.list).forEach(v => {
            arr.push({
              label: data.list[v],
              value: v,
            });
          });
          resolve(arr);
        }
      } else {
        resolve([]);
      }
    });
  });
}
export async function getRuleDetail(params) {
  const { isZyAccount, ...rest } = params;
  return request(
    isZyAccount ? '/Api/ChinaPost/Dak/complateRulesDetail' : '/Api/YZ/League/complateRulesDetail',
    {
      method: 'POST',
      body: rest,
    },
  );
}
export async function setRuleDetail(params) {
  const { isZyAccount, ...rest } = params;
  return request(
    isZyAccount ? '/Api/ChinaPost/Dak/complateRulesAdd' : '/Api/YZ/League/complateRulesAdd',
    {
      method: 'POST',
      body: rest,
    },
  );
}

// 删除代入库
export async function deleteMixin(params) {
  const { isZyAccount = false, ...rest } = params;
  return request(isZyAccount ? '/Api/ChinaPost/Dak/complateDel' : '/Api/YZ/League/complateDel', {
    method: 'POST',
    body: rest,
  });
}

export async function getGraphicCode(params) {
  const { isZyAccount = false, ...rest } = params;
  return request(
    isZyAccount ? '/Api/ChinaPost/Dak/getGraphicCode ' : '/Api/YZ/League/getGraphicCode',
    {
      method: 'POST',
      body: rest,
    },
  );
}
