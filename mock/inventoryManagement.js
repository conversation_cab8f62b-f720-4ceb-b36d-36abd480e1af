/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import mockjs from 'mockjs';

const { mock } = mockjs;

/**
 * 库存记录
 *  */
export default {
  // 获取列表
  'POST /Api/YZ/CourierStation/getStockList': (req, res) => {
    const list = [];

    Array.from({ length: 30 }).forEach((_, index) => {
      list.push({
        id: index,
        waybill_no: `${index}123456`,
        brand: 'sf',
        brandName: '邮政',
        note: '短信记录备注',
        pickup_code: '1',
        express_phone: '188****0094',
        user_name: '收件人姓名',
        storage_time: '2020-09-25 15:39:22',
        out_time: '2020-09-25 15:39:22',
        back_time: '2020-09-25 15:39:22',
        status: '1',
        status_cn: '待出库',
        upload_state: '1',
        upload_state_cn: '上传失败',
        sms_state: '1',
        sms_state_cn: '短信已发送',
        ivr_status: '0',
        ivr_state_cn: '',
        storage_img_url: [
          'http://upload.kuaidihelp.com/oss_kbyzwaybill/waybill/2021_09/24/194448819524705614d90b1da68f4209266912.jpg?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,size_30,text_MjAyMS0wOS0yNCAxNjo0Nzo0NQ==,color_FFFFFF,shadow_50,t_100,g_se,x_10,y_10&',
          'http://upload.kuaidihelp.com/inn_ocr/2021/09/24/16/46/8330ea39afa4615e6b9675068a81b72e.jpeg',
        ],
        out_img_url: [
          'http://upload.kuaidihelp.com/oss_kbyzwaybill/waybill/2021_09/24/717545487518209614d911c6376d6257009881.jpg?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,size_30,text_MjAyMS0wOS0yNCAxNjo0OTozMg==,color_FFFFFF,shadow_50,t_100,g_se,x_10,y_10&',
        ],
        img_stat: 0,
      });
    });

    res.send({
      msg: '成功',
      code: 0,
      data: {
        list,
        page: 1,
        pageSize: 10,
        total: 30,
      },
    });
  },
  // 导出
  'POST /Api/YZ/CourierStation/export': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 获取扫描记录
  'POST /Api/YZ/StockManage/getWaybillRemovelist': (req, res) => {
    res.send(mock({
      msg: '成功',
      code: 0,
      data: {
        'list|20': [
          {
            id: '@id',
            cm_id: '@id',
            slave_cm_id: '@id',
            waybill: '@id',
            brand: 'jt',
            phone: /^1[3-9]\d{9}$/,
            pickupCode: '@integer(100000, 999999)',
            devInfo: '@cword(10)',
            scan_time: '@datetime',
            create_time: '@datetime',
            type: '1',
            source: '@cword(10)',
            type_cn: '@cword(10)',
          },
        ],
        page: 1,
        pageSize: 10,
        total: 24,
      },
    }));
  },
};
